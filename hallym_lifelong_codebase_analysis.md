# 🏗️ Hallym Lifelong Learning System - 코드베이스 분석

## 📋 프로젝트 개요

- **프로젝트명**: Hallym Lifelong Learning System (한림평생교육원 시스템)
- **기술 스택**: Spring Framework 5.3.6 + eGovFramework 4.0.0 기반 Java 웹 애플리케이션
- **빌드 도구**: Maven
- **데이터베이스**: Oracle, MySQL, MariaDB, PostgreSQL 지원
- **Java 버전**: 1.8
- **패키징**: WAR (Web Application Archive)

## 🏛️ 아키텍처 구조

### 패키지 구조

```
com.intermorph/
├── cmmn/           # 공통 모듈 (Common)
│   ├── base/       # 기본 클래스
│   ├── rsce/       # 리소스 관리
│   ├── service/    # 공통 서비스
│   ├── util/       # 유틸리티
│   └── web/        # 공통 웹 컨트롤러
├── crs/            # 과정(Course) 관리
│   ├── crs/        # 운영 과정
│   ├── mstr/       # 마스터 과정
│   ├── aplcnt/     # 수강 신청
│   ├── sbj/        # 과목 관리
│   └── study/      # 학습 관리
├── cnts/           # 콘텐츠 관리
│   ├── onl/        # 온라인 콘텐츠
│   ├── offl/       # 오프라인 콘텐츠
│   ├── tst/        # 시험
│   └── srvy/       # 설문
├── fctls/          # 시설 관리
├── space/          # 공간 관리
├── stlm/           # 결제/정산 (Settlement)
├── uss/            # 사용자 서비스 (User Service)
├── wokasi/         # 워크넷 연계
├── hallym/         # 한림 특화 기능
└── stats/          # 통계
```

### 기술 스택 상세

#### 백엔드 프레임워크
- **Spring Framework**: 5.3.6
- **eGovFramework**: 4.0.0 (정부 표준 프레임워크)
- **MyBatis**: 데이터 액세스 레이어
- **Maven**: 빌드 및 의존성 관리

#### 데이터베이스
- **Oracle**: 19.8.0.0 (ojdbc8)
- **MySQL**: 5.1.31
- **MariaDB**: 2.2.5
- **PostgreSQL**: 9.4.1208
- **H2**: 1.4.180 (테스트용)

#### 보안
- **Spring Security**: 인증/인가
- **Jasypt**: 암호화 (1.9.3)
- **GPKI**: 공인인증서 연동
- **SHA-256**: 패스워드 해싱

## 🎯 핵심 기능 모듈

### 1. 리소스 관리 시스템 (IMRsceMngController)

**주요 기능:**
- 파일 업로드/다운로드
- 미디어 리소스 관리
- 버전 관리 및 변경 이력
- FTP 기반 파일 저장

**핵심 클래스:**
```java
@Controller
public class IMRsceMngController extends BaseController {
    @Resource(name = "IMRsceService")
    private IMRsceService rsceService;
    
    // 리소스 등록/수정/삭제 기능
}
```

### 2. 과정 관리 시스템

#### 마스터 과정 (IMCrsMstrMngController)
- 과정 템플릿 관리
- 과정 기본 정보 설정
- 과정 분류 및 카테고리 관리

#### 운영 과정 (IMCrsMngController)
- 실제 운영되는 과정 관리
- 수강생 모집 및 관리
- 출석 및 성적 관리
- 과정 일정 관리

### 3. 콘텐츠 관리 시스템
- **온라인 콘텐츠**: 동영상, 문서, 멀티미디어
- **오프라인 콘텐츠**: 강의실 수업 자료
- **시험 관리**: 온라인 시험, 채점
- **설문 관리**: 만족도 조사, 피드백

### 4. 사용자 관리 시스템
- **회원 관리**: 가입, 정보 수정, 탈퇴
- **권한 관리**: 관리자, 강사, 수강생
- **마이페이지**: 수강 이력, 성적 조회

## 🗄️ 데이터베이스 설정

### 다중 데이터베이스 지원

```xml
<!-- MySQL 설정 -->
<beans profile="mysql">  
    <bean id="dataSource" class="org.apache.commons.dbcp2.BasicDataSource">
        <property name="driverClassName" value="${Globals.mysql.DriverClassName}"/>
        <property name="url" value="${Globals.mysql.Url}" />
        <property name="username" value="${Globals.mysql.UserName}"/>
        <property name="password" value="#{egovEnvCryptoService.getPassword()}"/>
    </bean>
</beans>
```

### 보안 설정 (암호화)

```xml
<egov-crypto:config id="egovCryptoConfig" 
    initial="true"
    crypto="true"
    algorithm="SHA-256"
    algorithmKey="imlmsframe"
    cryptoBlockSize="1024"
/>
```

## 🛠️ 개발 환경 및 도구

### 테스트 프레임워크
- **JUnit**: 4.13.2 (단위 테스트)
- **Spock**: 2.3-groovy-4.0 (BDD 스타일 테스트)
- **Mockito**: 3.9.0 (모킹)
- **Selenide**: 6.19.1 (UI 자동화 테스트)

### 빌드 및 배포
- **Maven**: 빌드 도구
- **Tomcat 7**: 애플리케이션 서버
- **JaCoCo**: 코드 커버리지 측정
- **PMD**: 정적 코드 분석

### 코드 품질 관리
- **Lombok**: 1.18.30 (코드 간소화)
- **Maven Site Plugin**: 문서화
- **Failsafe Plugin**: 통합 테스트

## 📊 주요 의존성

### 핵심 라이브러리
```xml
<!-- Spring Framework -->
<dependency>
    <groupId>org.springframework</groupId>
    <artifactId>spring-webmvc</artifactId>
    <version>5.3.6</version>
</dependency>

<!-- eGovFramework -->
<dependency>
    <groupId>org.egovframe.rte</groupId>
    <artifactId>org.egovframe.rte.ptl.mvc</artifactId>
    <version>4.0.0</version>
</dependency>

<!-- JSON 처리 -->
<dependency>
    <groupId>com.fasterxml.jackson.core</groupId>
    <artifactId>jackson-databind</artifactId>
    <version>2.11.4</version>
</dependency>
```

### 특화 라이브러리
- **Apache Tiles**: 3.0.8 (레이아웃 관리)
- **CKEditor**: 3.5.3 (WYSIWYG 에디터)
- **Quartz**: 2.3.2 (스케줄링)
- **Apache Commons**: 파일 처리, 압축 등

## 🎨 프론트엔드

### 뷰 기술
- **JSP/JSTL**: 서버 사이드 템플릿
- **Apache Tiles**: 레이아웃 관리
- **jQuery**: JavaScript 라이브러리
- **Ajax**: 비동기 통신

### UI 컴포넌트
- **CKEditor**: 리치 텍스트 에디터
- **jQuery UI**: UI 위젯
- **Bootstrap**: CSS 프레임워크 (추정)

## 📈 특화 기능

### 1. 워크넷 연계 시스템
- 직업훈련 과정 연계
- 훈련생 정보 동기화
- 수료 정보 전송

### 2. 인증 시스템
- **실명인증**: 본인인증 서비스 (NiceID, IPIN)
- **공인인증서**: GPKI 연동
- **소셜 로그인**: 페이스북, 네이버, 구글

### 3. 결제 시스템
- 다양한 결제 수단 지원
- 환불 처리
- 정산 관리

### 4. 알림 시스템
- **SMS**: 문자 메시지 발송
- **알림톡**: 카카오 알림톡
- **이메일**: 메일 발송

## 🔧 시스템 설정

### 프로파일 설정
- **dev**: 개발 환경 (기본)
- **service**: 운영 환경
- **sadmin**: 시스템 관리자 환경

### 로깅 설정
- **Log4j2**: 로깅 프레임워크
- **log4jdbc**: SQL 로깅

## 📝 개발 가이드

### 문서화
- **README.md**: 프로젝트 개요
- **Maven Site**: 자동 문서 생성
- **JavaDoc**: API 문서

### 코딩 표준
- **Lombok** 사용 권장
- **Spock** 테스트 작성
- **Git 브랜치 관리 정책** 준수

## 🚀 배포 및 운영

### 배포 방식
- **WAR 파일**: `egovframework-all-in-one.war`
- **Tomcat 배포**: Maven Tomcat Plugin 사용
- **Docker 지원**: 컨테이너 배포 가능

### 모니터링
- **JaCoCo**: 테스트 커버리지
- **Maven Site**: 프로젝트 리포트
- **로그 모니터링**: Log4j2 기반

---

## 📞 연락처 및 지원

이 시스템은 **한림평생교육원**의 종합적인 학습관리시스템(LMS)으로, 과정 관리부터 수강생 관리, 콘텐츠 관리, 결제까지 교육기관 운영에 필요한 모든 기능을 포함하고 있습니다. eGovFramework 기반으로 구축되어 정부 표준 프레임워크의 안정성과 보안성을 확보하고 있습니다.
