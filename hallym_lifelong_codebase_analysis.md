# 🏗️ Hallym Lifelong Learning System - 코드베이스 분석

## 🏢 FCTLS 패키지 상세 분석

### 📋 FCTLS 패키지 개요
FCTLS(Facilities) 패키지는 한림평생교육원의 시설 관리 시스템을 담당하는 핵심 모듈입니다.

#### 주요 모듈 구성
```
com.intermorph.fctls/
├── fcltsMng/        # 시설물 관리 (Facilities Management)
├── fcltsSchdl/      # 시설 스케줄 관리 (Facilities Schedule)
├── locker/          # 사물함 관리 (Locker Management)
└── lockerRental/    # 사물함 대여 관리 (Locker Rental)
```

### 🏢 1. 시설물 관리 (fcltsMng)

#### 핵심 기능
- **시설물 등록/수정/삭제**: 강의실, 실습실, 기숙사 등 시설물 정보 관리
- **시설물 분류**: 장소구분, 시설물구분, 층수별 관리
- **수용인원 관리**: 시설별 최대 수용 가능 인원 설정
- **시설 상태 관리**: 사용가능/불가능 상태 및 기간 설정

#### 주요 클래스 구조
```java
// VO (Value Object)
public class IMFcltsMngVO extends BaseVO {
    private String fcltsMngId;        // 시설물관리ID
    private String placeDvsnCdv;      // 장소구분코드값
    private String fcltsDvsnCdv;      // 시설물구분코드값
    private String fcltsNm;           // 시설물명
    private String floorCdv;          // 층수
    private String acmdPrsnl;         // 수용인원
    private String sttsCdv;           // 상태코드값
    private String notAvlblBgngDt;    // 사용불가시작일시
    private String notAvlblEndDt;     // 사용불가종료일시
    private String useYn;             // 사용여부
}

// 컨트롤러
@Controller
public class IMFcltsMngMngController extends BaseController {
    @Resource(name = "IMFcltsMngService")
    private IMFcltsMngService fcltsMngService;

    // 시설물 등록/수정/삭제/조회 기능
}

// 서비스 인터페이스
public interface IMFcltsMngService {
    int insertFcltsMng(IMFcltsMngVO vo) throws Exception;
    int updateFcltsMng(IMFcltsMngVO vo) throws Exception;
    int deleteFcltsMng(IMFcltsMngVO vo) throws Exception;
    BasePage<BaseResultSet> selectListFcltsMng(BaseCondition condition) throws Exception;
    BaseResultSet selectDetailFcltsMng(IMFcltsMngVO vo) throws Exception;
}
```

#### 데이터베이스 테이블 구조
```sql
-- im_fclts_mng 테이블
CREATE TABLE im_fclts_mng (
    fclts_mng_id VARCHAR,      -- 시설물관리ID (PK)
    place_dvsn_cdv VARCHAR,    -- 장소구분코드값
    fclts_dvsn_cdv VARCHAR,    -- 시설물구분코드값
    fclts_nm VARCHAR,          -- 시설물명
    floor_cdv VARCHAR,         -- 층수
    acmd_prsnl VARCHAR,        -- 수용인원
    stts_cdv VARCHAR,          -- 상태코드값
    not_avlbl_bgng_dt VARCHAR, -- 사용불가시작일시
    not_avlbl_end_dt VARCHAR,  -- 사용불가종료일시
    use_yn VARCHAR,            -- 사용여부
    del_yn VARCHAR,            -- 삭제여부
    -- 공통 감사 필드들...
);
```

### 📅 2. 시설 스케줄 관리 (fcltsSchdl)

#### 핵심 기능
- **시설 예약 스케줄링**: 시설별 예약 현황 관리
- **월별/일별 스케줄 조회**: 캘린더 형태의 스케줄 표시
- **연간 계획 연동**: 과정 운영 계획과 시설 배정 연계
- **시설 사용률 분석**: 시설별 사용 현황 통계

#### 주요 기능
```java
@Controller
public class IMFcltsSchdlMngController extends BaseController {

    /**
     * 월별 시설 스케줄 조회
     */
    @RequestMapping(value = "/mng/fcltsSchdl/selectListFcltsSchdlByMonth.do")
    public ModelAndView selectListFcltsSchdlByMonth() {
        // 시설물 리스트 조회
        IMFcltsMngCondition c = new IMFcltsMngCondition();
        c.setScUseYn("Y");                    // 사용 가능한 시설만
        c.setScFcltsDvsnCdvNot("02");        // 기숙사 제외
        List<BaseResultSet> fcltsList = fcltsMngService.selectListFcltsMng(c).getList();

        // 연간계획 데이터와 연동
        List<IMFcltsSchdlVO> crdlList = this.getCrdlList(calendar, calendarEnd, condition.getScPlaceDvsnCdv());

        // 월별 사용 현황 요약
        List<IMFcltsSchdlSmyVO> smyList = this.getSmyList(calStart, calEnd, crdlList, fcltsList, condition.getScPlaceDvsnCdv());
    }
}
```

### 🗄️ 3. 사물함 관리 (locker)

#### 핵심 기능
- **사물함 등록/관리**: 사물함 번호, 타입, 가격 설정
- **사물함 상태 관리**: 정상, 고장, 분실 상태 관리
- **사물함 현황 조회**: 층별, 상태별 사물함 현황

#### 주요 클래스 구조
```java
// 사물함 VO
public class IMLockerVO extends BaseVO {
    private String lockerId;        // 사물함 ID
    private String lockerNumber;    // 사물함 번호
    private String lockerType;      // 사물함구분
    private String price;           // 단가
    private String lockerStatus;    // 사물함상태 (정상/고장/분실)
}

// 사물함 서비스
public interface IMLockerService {
    int insertLocker(IMLockerVO vo) throws Exception;
    int updateLocker(IMLockerVO vo) throws Exception;
    int deleteLocker(IMLockerVO vo) throws Exception;
    List<BaseResultSet> selectListLocker(BaseCondition condition) throws Exception;
    BaseResultSet selectDetailLocker(IMLockerVO vo) throws Exception;

    // 사물함 상태별 개수 조회
    Map<String, Integer> getLockerCount(BaseCondition condition) throws Exception;

    // 사물함 사용 중 여부 확인
    boolean isLockerInUse(IMLockerVO iMLocker) throws Exception;
}
```

#### 데이터베이스 구조
```sql
-- im_locker 테이블
CREATE TABLE im_locker (
    locker_id VARCHAR,      -- 사물함 ID (PK)
    locker_number VARCHAR,  -- 사물함 번호
    locker_type VARCHAR,    -- 사물함구분
    price VARCHAR,          -- 단가
    locker_status VARCHAR,  -- 사물함상태
    del_yn VARCHAR,         -- 삭제여부
    -- 공통 감사 필드들...
);
```

### 🏪 4. 사물함 대여 관리 (lockerRental)

#### 핵심 기능
- **사물함 대여 신청**: 사용자별 사물함 대여 처리
- **대여 기간 관리**: 시작일, 종료일, 연장 처리
- **결제 연동**: 대여료 결제 및 환불 처리
- **반납 관리**: 정상 반납, 연체, 키 분실 처리
- **연체료/분실료 관리**: 추가 요금 처리

#### 주요 클래스 구조
```java
// 사물함 대여 VO
public class IMLockerRentalVO extends BaseVO {
    private String rentalId;        // 이용내역ID
    private String lockerId;        // 사물함 ID
    private String esntlId;         // 고유ID (사용자)
    private String startDate;       // 임대 시작일
    private String endDate;         // 임대 종료일
    private String stlmMethod;      // 결제수단
    private String totalAmount;     // 결제금액
    private String rentalStatus;    // 신청상태
    private String paymentDate;     // 결제일
    private String requestDate;     // 신청일
    private String returnDate;      // 반납일
    private String keyLossYn;       // 키분실여부
    private String keyLossAmount;   // 키분실수수료
    private String useYn;           // 사용여부
}

// 대여 서비스 구현
@Service("IMLockerRentalService")
public class IMLockerRentalServiceImpl implements IMLockerRentalService {

    public int insertLockerRental(IMLockerRentalVO vo) throws Exception {
        // 대여 ID 생성
        vo.setRentalId(idgenService.getNextStringId());

        // 대여 기간 계산 (개월 단위)
        LocalDate leaseStartDate = LocalDate.parse(vo.getStartDate());
        int months = Integer.parseInt(vo.getMonthCount());
        LocalDate leaseEndDate = leaseStartDate.plusMonths(months);
        vo.setEndDate(leaseEndDate.toString());

        return lockerRentalMapper.insert(vo);
    }
}
```

#### 복합 쿼리 예시 (사물함 상태 조회)
```sql
-- 사물함별 현재 상태 조회 (정상/사용중/미반납/고장/분실)
SELECT
    loc.locker_id,
    loc.locker_number,
    CASE
        WHEN MAX(CASE WHEN ren.end_date < NOW() AND ren.return_date IS NULL THEN 1 ELSE 0 END) = 1
        THEN '미반납'
        WHEN MAX(CASE WHEN ren.use_yn = 'Y' THEN 1 ELSE 0 END) = 1
        THEN '사용중'
        WHEN loc.locker_status = '고장'
        THEN '고장'
        WHEN loc.locker_status = '분실'
        THEN '분실'
        ELSE '정상'
    END AS locker_status,
    DATE_FORMAT(ren.end_date, '%m-%d') AS end_date
FROM im_locker loc
LEFT JOIN im_locker_rental ren ON loc.locker_id = ren.locker_id
WHERE loc.del_yn = 'N'
GROUP BY loc.locker_id, loc.locker_number;
```

### 🔄 시스템 워크플로우

#### 1. 시설물 관리 워크플로우
```
시설물 등록 → 시설 정보 설정 → 사용 가능 상태 설정 → 스케줄 배정 가능
```

#### 2. 시설 예약 워크플로우
```
과정 계획 수립 → 시설 배정 요청 → 시설 가용성 확인 → 예약 확정 → 스케줄 등록
```

#### 3. 사물함 대여 워크플로우
```
사물함 신청 → 가용 사물함 확인 → 결제 처리 → 대여 시작 → 사용 중 → 반납 처리
```

#### 4. 사물함 상태 관리
```
정상 ↔ 사용중 ↔ 미반납
  ↓      ↓       ↓
고장 → 수리 → 정상
  ↓
분실 → 교체 → 정상
```

### 📊 주요 특징

#### 1. **통합 관리**
- 시설물과 사물함을 통합적으로 관리
- 예약 시스템과 연동된 스케줄 관리
- 결제 시스템과 연계된 대여 관리

#### 2. **상태 기반 관리**
- 시설물: 사용가능/불가능, 예약중/사용중
- 사물함: 정상/고장/분실/사용중/미반납

#### 3. **기간 관리**
- 시설 사용불가 기간 설정
- 사물함 대여 기간 자동 계산
- 연체 관리 및 추가 요금 처리

#### 4. **리포팅 기능**
- 시설 사용률 통계
- 사물함 현황 대시보드
- 월별/연간 사용 현황

이 FCTLS 패키지는 교육기관의 물리적 자원(시설, 사물함)을 효율적으로 관리하고, 사용자에게 편리한 예약 및 대여 서비스를 제공하는 종합적인 시설 관리 시스템입니다.

## 🎨 FCTLS 패키지 JSP 프론트엔드 분석

### 📁 JSP 파일 구조
```
WEB-INF/jsp/view/
├── mng/fclts/                    # 관리자 화면
│   ├── fcltsMng/                 # 시설물 관리
│   │   ├── selectListFcltsMng.jsp    # 시설물 목록
│   │   ├── registFcltsMng.jsp        # 시설물 등록
│   │   └── modifyFcltsMng.jsp        # 시설물 수정
│   ├── fcltsSchdl/               # 시설 스케줄 관리
│   │   ├── selectListFcltsSchdlByMonth.jsp  # 월별 스케줄
│   │   ├── registFcltsSchdl1.jsp     # 교육과정 예약
│   │   ├── registFcltsSchdl2.jsp     # 회의 예약
│   │   └── registFcltsSchdl3.jsp     # 외부 예약
│   ├── locker/                   # 사물함 관리
│   │   ├── selectListLocker.jsp      # 사물함 목록
│   │   ├── registLocker.jsp          # 사물함 등록
│   │   └── modifyLocker.jsp          # 사물함 수정
│   └── lockerRental/             # 사물함 대여 관리
│       ├── selectListLockerRental.jsp    # 대여 현황 (다이어그램)
│       ├── registLockerRental.jsp        # 대여 등록
│       ├── modifyLockerRental.jsp        # 대여 수정/반납
│       └── lockerUsageStatus/            # 사용 현황
└── user/fclts/                   # 사용자 화면
    ├── fcltsSchdl/               # 시설 예약
    └── lockerRental/             # 사물함 대여 신청
```

### 🏢 1. 시설물 관리 JSP 분석

#### 시설물 등록/수정 폼 구조
```jsp
<!-- registFcltsMng.jsp / modifyFcltsMng.jsp -->
<form:form modelAttribute="iMFcltsMng" name="iMFcltsMng" method="post">
<table class="tbl_row">
    <tr>
        <th scope="row">장소구분<span class="c_red">*</span></th>
        <td>
            <select name="placeDvsnCdv">
                <option value="">선택</option>
                <im:cd type="option" codeId="${arrPlaceDvsnCdv}"/>
            </select>
        </td>
    </tr>
    <tr>
        <th scope="row">시설물구분<span class="c_red">*</span></th>
        <td>
            <select name="fcltsDvsnCdv">
                <im:cd type="option" codeId="${arrFcltsDvsnCdv}"/>
            </select>
        </td>
    </tr>
    <!-- 시설물명, 층수, 수용인원, 상태 등 -->
</table>
</form:form>
```

#### 코드 값 정의
```jsp
<!-- incFcltsMng.jsp -->
<c:set var="arrPlaceDvsnCdv">01=교육원,02=서울교육장,03=대전교육센터</c:set>
<c:set var="arrFcltsDvsnCdv">01=강의장,02=기숙사,03=운동장,04=회의실</c:set>
<c:set var="arrSttsCdv">01=정상,02=고장,03=수리,04=기타</c:set>
<c:set var="arrFloorCdv">01=1층,02=2층,03=3층,04=4층,05=5층,99=층수없음</c:set>
```

### 📅 2. 시설 스케줄 관리 JSP 분석

#### 예약 유형별 등록 화면
```javascript
// registFcltsSchdl1.jsp (교육과정 예약)
var REQ = {
    init: function() {
        // 시설물 목록 Ajax 조회
        this.req.fcltsList = imRequest("ajax", {formId: "FormFcltsMng"});
        this.req.fcltsList.cfg.url = "/mng/fcltsMng/selectListAjax.do";
        this.req.fcltsList.cfg.fn.complete = function(act, data) {
            $("#fcltsMngId option").remove();
            if (data != null && data.result.length > 0) {
                for(var i=0; i<data.result.length; i++) {
                    $("#fcltsMngId").append(
                        "<option value='" + data.result[i].fcltsMng.fcltsMngId +
                        "' prsnl='" + data.result[i].fcltsMng.acmdPrsnl + "'>" +
                        data.result[i].fcltsMng.fcltsNm + "</option>"
                    );
                }
            }
        };
    },

    // 시설물 선택 시 수용인원 자동 설정
    fcltsMngChange: function() {
        var prsnl = $('#fcltsMngId option:selected').attr('prsnl');
        $("input[name='prsnl']").val(prsnl);
    }
};
```

#### 예약 구분별 처리
```jsp
<!-- 예약구분 라디오 버튼 -->
<im:cd type="radio" name="rsrvtCdv" codeId="${arrRsrvtCdv}"
       selectedCode="01" onclick="REQ.rsrvtCdvChange(this.value)"/>

<!-- registFcltsSchdl1.jsp: 교육과정 (selectedCode="01") -->
<!-- registFcltsSchdl2.jsp: 회의 (selectedCode="02") -->
<!-- registFcltsSchdl3.jsp: 외부 (selectedCode="03") -->
```

### 🗄️ 3. 사물함 관리 JSP 분석

#### 사물함 등록/수정 폼
```jsp
<!-- registLocker.jsp / modifyLocker.jsp -->
<form:form modelAttribute="iMLocker" name="iMLocker" method="post">
<table class="tbl_row">
    <tr>
        <th scope="row">사물함번호<span class="c_red">*</span></th>
        <td><form:input path="lockerNumber"/></td>
    </tr>
    <tr>
        <th scope="row">사물함구분<span class="c_red">*</span></th>
        <td>
            <select name="lockerType">
                <im:cd type="option" codeId="${arrTypeCdv}"/>
            </select>
        </td>
    </tr>
    <tr>
        <th scope="row">단가<span class="c_red">*</span></th>
        <td><form:input path="price"/></td>
    </tr>
    <tr>
        <th scope="row">사물함상태</th>
        <td>
            <select name="lockerStatus">
                <im:cd type="option" codeId="${arrSttsCdv}"/>
            </select>
        </td>
    </tr>
</table>
</form:form>
```

#### JavaScript 유효성 검사
```javascript
// modifyLocker.jsp
this.req.save.validator.set({
    title: "사물함번호",
    name: "lockerNumber",
    data: ["!null", "number", "!space"]  // 필수, 숫자, 공백불가
});
```

### 🏪 4. 사물함 대여 관리 JSP 분석

#### 사물함 현황 다이어그램 (selectListLockerRental.jsp)
```jsp
<!-- 1층 사물함 다이어그램 (1-126번) -->
<table class="locker_diagram">
    <c:forEach var="row" begin="0" end="17">
        <tr>
            <c:forEach var="col" begin="0" end="6">
                <c:set var="num" value="${1 + (col + (row * 7))}" />
                <c:set var="item" value="${list[num-1]}" />
                <td class="locker_cell
                    <c:choose>
                        <c:when test='${item.locker.lockerStatus == "사용중"}'>locker_used</c:when>
                        <c:when test='${item.locker.lockerStatus == "미반납"}'>locker_overdue</c:when>
                        <c:when test='${item.locker.lockerStatus == "고장"}'>locker_broken</c:when>
                        <c:when test='${item.locker.lockerStatus == "분실"}'>locker_lost</c:when>
                        <c:otherwise>locker_available</c:otherwise>
                    </c:choose>">

                    <div class="locker_number">
                        <a href="#" onclick="REQ.modify({'rentalId': '${item.lockerRental.rentalId}', 'lockerId': '${item.locker.lockerId}'}); return false;">
                            ${num}
                        </a>
                    </div>

                    <div class="locker_under">
                        <c:if test="${item.locker.lockerStatus == '사용중'}">
                            ${item.lockerRental.endDate}
                        </c:if>
                    </div>
                </td>
            </c:forEach>
        </tr>
    </c:forEach>
</table>

<!-- 2층 사물함 다이어그램 (127-252번) -->
<table class="locker_diagram">
    <c:forEach var="row" begin="0" end="6">
        <tr>
            <c:forEach var="col" begin="0" end="27">
                <c:set var="num" value="${127 + (row + (col * 7))}" />
                <c:set var="item" value="${list[num-1]}" />
                <!-- 동일한 구조 반복 -->
            </c:forEach>
        </tr>
    </c:forEach>
</table>
```

#### 사물함 대여 등록 (registLockerRental.jsp)
```javascript
var REQ = {
    init: function() {
        // 대여료 계산 로직
        this.req.save.validator.set({
            title: "성명",
            name: "mberNm",
            data: ["!null"]
        });

        this.req.save.validator.set({
            title: "임대 시작일",
            name: "startDate",
            data: ["!null"]
        });
    },

    // 회원 정보 설정 (팝업에서 선택)
    setMmbr: function(obj) {
        $("#esntlId").val(obj.esntlId);
        $("#mberId").val(obj.mberId);
        $("#mberNm").val(obj.mberNm);
        $("#mbtlnum").val(obj.moblphonNo);
    },

    // 총 결제금액 계산
    totalAmount: function() {
        var rentalPrice = ${locker.locker.price}; // 월 임대료
        var monthCount = $("#monthCount").val();   // 대여 개월 수
        var totalAmount = rentalPrice * monthCount;
        $("#totalAmount").val(totalAmount);
    }
};
```

#### 사물함 반납 처리 (modifyLockerRental.jsp)
```javascript
var REQ = {
    init: function() {
        // 반납 처리
        this.req.save = imRequest("ajax", {formId: "iMLockerRental"});
        this.req.save.cfg.url = "/mng/lockerRental/update.do";

        // 환불 처리
        this.req.saveRefund = imRequest("ajax", {formId: "iMLockerRefund"});
        this.req.saveRefund.cfg.url = "/mng/lockerRefund/insert.do";

        // 연체료 처리
        this.req.saveOverdue = imRequest("ajax", {formId: "iMLockerOverdue"});
        this.req.saveOverdue.cfg.url = "/mng/lockerOverdue/insert.do";
    },

    // 키 분실 여부에 따른 폼 표시/숨김
    keyLossChange: function(value) {
        if (value === 'Y') {
            $("#keyLossAmountDiv").show();
            $("#keyLossAmount").attr("required", true);
        } else {
            $("#keyLossAmountDiv").hide();
            $("#keyLossAmount").removeAttr("required");
        }
    },

    // 반납 상태에 따른 폼 전환
    toggleForm: function() {
        var returnDate = $("#returnDate").val();
        if (returnDate) {
            $("#returnBtn").hide();
            $("#returnCancelBtn").show();
        } else {
            $("#returnBtn").show();
            $("#returnCancelBtn").hide();
        }
    }
};
```

### 🎯 5. 공통 JavaScript 패턴

#### imRequest 패턴 (Ajax 통신)
```javascript
// 표준 Ajax 요청 패턴
var REQ = {
    req: {
        list: null,
        save: null,
        modify: null,
        delete: null
    },

    init: function() {
        // 목록 조회
        this.req.list = imRequest();
        this.req.list.cfg.formId = "FormPageDetail";
        this.req.list.cfg.url = "/mng/moduleName/selectList.do";

        // 저장/수정
        this.req.save = imRequest("ajax", {formId: "formName"});
        this.req.save.cfg.type = "json";
        this.req.save.cfg.url = "/mng/moduleName/insert.do";
        this.req.save.cfg.message.confirm = "등록하시겠습니까?";
        this.req.save.cfg.fn.complete = function(act, data) {
            if (data != null && data.result > 0) {
                IMGLBObject.request.list(); // 목록으로 돌아가기
            } else {
                COMMT.errorMessage();
            }
        };
    }
};
```

#### 유효성 검사 패턴
```javascript
// 클라이언트 사이드 유효성 검사
this.req.save.validator.set({
    title: "필드명",
    name: "fieldName",
    data: ["!null", "number", "!space", "minLength:2", "maxLength:50"]
});

// 서버 사이드 유효성 검사 (Spring Validator)
<validator:javascript formName="iMModuleName" staticJavascript="false" xhtml="true" cdata="false"/>
```

### 🎨 6. UI/UX 특징

#### CSS 클래스 체계
```css
/* 사물함 상태별 스타일 */
.locker_available { background-color: #e8f5e8; }  /* 사용가능 - 연한 녹색 */
.locker_used { background-color: #ffe8e8; }       /* 사용중 - 연한 빨간색 */
.locker_overdue { background-color: #ff6b6b; }    /* 미반납 - 빨간색 */
.locker_broken { background-color: #ffd93d; }     /* 고장 - 노란색 */
.locker_lost { background-color: #6c757d; }       /* 분실 - 회색 */

/* 공통 테이블 스타일 */
.tbl_row { width: 100%; border-collapse: collapse; }
.tbl_row th { background-color: #f8f9fa; text-align: left; padding: 10px; }
.tbl_row td { padding: 10px; border-bottom: 1px solid #dee2e6; }

/* 필수 입력 표시 */
.c_red { color: #dc3545; }
```

#### 반응형 레이아웃
```jsp
<!-- 사물함 다이어그램 반응형 처리 -->
<div class="locker_container">
    <div class="locker_legend">
        <span class="legend_item available">사용가능</span>
        <span class="legend_item used">사용중</span>
        <span class="legend_item overdue">미반납</span>
        <span class="legend_item broken">고장</span>
        <span class="legend_item lost">분실</span>
    </div>

    <!-- 사물함 다이어그램 테이블 -->
</div>
```

### 🔄 7. 데이터 흐름 및 상태 관리

#### 사물함 상태 전환 로직
```javascript
// 사물함 상태 변경 시 실시간 업데이트
function updateLockerStatus(lockerId, newStatus) {
    var req = imRequest("ajax");
    req.cfg.url = "/mng/locker/updateStatus.do";
    req.cfg.data = {
        lockerId: lockerId,
        lockerStatus: newStatus
    };
    req.cfg.fn.complete = function(act, data) {
        if (data.result > 0) {
            // 화면 새로고침 없이 상태 업데이트
            updateLockerCell(lockerId, newStatus);
        }
    };
    req.go();
}
```

#### 실시간 예약 현황 업데이트
```javascript
// 시설 예약 시 충돌 검사
function checkReservationConflict(fcltsMngId, startDate, endDate) {
    var req = imRequest("ajax");
    req.cfg.url = "/mng/fcltsSchdl/checkConflict.do";
    req.cfg.data = {
        fcltsMngId: fcltsMngId,
        startDate: startDate,
        endDate: endDate
    };
    req.cfg.fn.complete = function(act, data) {
        if (data.hasConflict) {
            COMMT.message("해당 시간에 이미 예약이 있습니다.");
            return false;
        }
        // 예약 진행
        proceedReservation();
    };
    req.go();
}
```

이러한 JSP 프론트엔드 구조는 사용자 친화적인 인터페이스와 효율적인 데이터 관리를 통해 FCTLS 시설 관리 시스템의 완성도를 높이고 있습니다.

---

## 📋 프로젝트 개요

- **프로젝트명**: Hallym Lifelong Learning System (한림평생교육원 시스템)
- **기술 스택**: Spring Framework 5.3.6 + eGovFramework 4.0.0 기반 Java 웹 애플리케이션
- **빌드 도구**: Maven
- **데이터베이스**: Oracle, MySQL, MariaDB, PostgreSQL 지원
- **Java 버전**: 1.8
- **패키징**: WAR (Web Application Archive)

## 🏛️ 아키텍처 구조

### 패키지 구조

```
com.intermorph/
├── cmmn/           # 공통 모듈 (Common)
│   ├── base/       # 기본 클래스
│   ├── rsce/       # 리소스 관리
│   ├── service/    # 공통 서비스
│   ├── util/       # 유틸리티
│   └── web/        # 공통 웹 컨트롤러
├── crs/            # 과정(Course) 관리
│   ├── crs/        # 운영 과정
│   ├── mstr/       # 마스터 과정
│   ├── aplcnt/     # 수강 신청
│   ├── sbj/        # 과목 관리
│   └── study/      # 학습 관리
├── cnts/           # 콘텐츠 관리
│   ├── onl/        # 온라인 콘텐츠
│   ├── offl/       # 오프라인 콘텐츠
│   ├── tst/        # 시험
│   └── srvy/       # 설문
├── fctls/          # 시설 관리
├── space/          # 공간 관리
├── stlm/           # 결제/정산 (Settlement)
├── uss/            # 사용자 서비스 (User Service)
├── wokasi/         # 워크넷 연계
├── hallym/         # 한림 특화 기능
└── stats/          # 통계
```

### 기술 스택 상세

#### 백엔드 프레임워크
- **Spring Framework**: 5.3.6
- **eGovFramework**: 4.0.0 (정부 표준 프레임워크)
- **MyBatis**: 데이터 액세스 레이어
- **Maven**: 빌드 및 의존성 관리

#### 데이터베이스
- **Oracle**: 19.8.0.0 (ojdbc8)
- **MySQL**: 5.1.31
- **MariaDB**: 2.2.5
- **PostgreSQL**: 9.4.1208
- **H2**: 1.4.180 (테스트용)

#### 보안
- **Spring Security**: 인증/인가
- **Jasypt**: 암호화 (1.9.3)
- **GPKI**: 공인인증서 연동
- **SHA-256**: 패스워드 해싱

## 🎯 핵심 기능 모듈

### 1. 리소스 관리 시스템 (IMRsceMngController)

**주요 기능:**
- 파일 업로드/다운로드
- 미디어 리소스 관리
- 버전 관리 및 변경 이력
- FTP 기반 파일 저장

**핵심 클래스:**
```java
@Controller
public class IMRsceMngController extends BaseController {
    @Resource(name = "IMRsceService")
    private IMRsceService rsceService;
    
    // 리소스 등록/수정/삭제 기능
}
```

### 2. 과정 관리 시스템

#### 마스터 과정 (IMCrsMstrMngController)
- 과정 템플릿 관리
- 과정 기본 정보 설정
- 과정 분류 및 카테고리 관리

#### 운영 과정 (IMCrsMngController)
- 실제 운영되는 과정 관리
- 수강생 모집 및 관리
- 출석 및 성적 관리
- 과정 일정 관리

### 3. 콘텐츠 관리 시스템
- **온라인 콘텐츠**: 동영상, 문서, 멀티미디어
- **오프라인 콘텐츠**: 강의실 수업 자료
- **시험 관리**: 온라인 시험, 채점
- **설문 관리**: 만족도 조사, 피드백

### 4. 사용자 관리 시스템
- **회원 관리**: 가입, 정보 수정, 탈퇴
- **권한 관리**: 관리자, 강사, 수강생
- **마이페이지**: 수강 이력, 성적 조회

## 🗄️ 데이터베이스 설정

### 다중 데이터베이스 지원

```xml
<!-- MySQL 설정 -->
<beans profile="mysql">  
    <bean id="dataSource" class="org.apache.commons.dbcp2.BasicDataSource">
        <property name="driverClassName" value="${Globals.mysql.DriverClassName}"/>
        <property name="url" value="${Globals.mysql.Url}" />
        <property name="username" value="${Globals.mysql.UserName}"/>
        <property name="password" value="#{egovEnvCryptoService.getPassword()}"/>
    </bean>
</beans>
```

### 보안 설정 (암호화)

```xml
<egov-crypto:config id="egovCryptoConfig" 
    initial="true"
    crypto="true"
    algorithm="SHA-256"
    algorithmKey="imlmsframe"
    cryptoBlockSize="1024"
/>
```

## 🛠️ 개발 환경 및 도구

### 테스트 프레임워크
- **JUnit**: 4.13.2 (단위 테스트)
- **Spock**: 2.3-groovy-4.0 (BDD 스타일 테스트)
- **Mockito**: 3.9.0 (모킹)
- **Selenide**: 6.19.1 (UI 자동화 테스트)

### 빌드 및 배포
- **Maven**: 빌드 도구
- **Tomcat 7**: 애플리케이션 서버
- **JaCoCo**: 코드 커버리지 측정
- **PMD**: 정적 코드 분석

### 코드 품질 관리
- **Lombok**: 1.18.30 (코드 간소화)
- **Maven Site Plugin**: 문서화
- **Failsafe Plugin**: 통합 테스트

## 📊 주요 의존성

### 핵심 라이브러리
```xml
<!-- Spring Framework -->
<dependency>
    <groupId>org.springframework</groupId>
    <artifactId>spring-webmvc</artifactId>
    <version>5.3.6</version>
</dependency>

<!-- eGovFramework -->
<dependency>
    <groupId>org.egovframe.rte</groupId>
    <artifactId>org.egovframe.rte.ptl.mvc</artifactId>
    <version>4.0.0</version>
</dependency>

<!-- JSON 처리 -->
<dependency>
    <groupId>com.fasterxml.jackson.core</groupId>
    <artifactId>jackson-databind</artifactId>
    <version>2.11.4</version>
</dependency>
```

### 특화 라이브러리
- **Apache Tiles**: 3.0.8 (레이아웃 관리)
- **CKEditor**: 3.5.3 (WYSIWYG 에디터)
- **Quartz**: 2.3.2 (스케줄링)
- **Apache Commons**: 파일 처리, 압축 등

## 🎨 프론트엔드

### 뷰 기술
- **JSP/JSTL**: 서버 사이드 템플릿
- **Apache Tiles**: 레이아웃 관리
- **jQuery**: JavaScript 라이브러리
- **Ajax**: 비동기 통신

### UI 컴포넌트
- **CKEditor**: 리치 텍스트 에디터
- **jQuery UI**: UI 위젯
- **Bootstrap**: CSS 프레임워크 (추정)

## 📈 특화 기능

### 1. 워크넷 연계 시스템
- 직업훈련 과정 연계
- 훈련생 정보 동기화
- 수료 정보 전송

### 2. 인증 시스템
- **실명인증**: 본인인증 서비스 (NiceID, IPIN)
- **공인인증서**: GPKI 연동
- **소셜 로그인**: 페이스북, 네이버, 구글

### 3. 결제 시스템
- 다양한 결제 수단 지원
- 환불 처리
- 정산 관리

### 4. 알림 시스템
- **SMS**: 문자 메시지 발송
- **알림톡**: 카카오 알림톡
- **이메일**: 메일 발송

## 🔧 시스템 설정

### 프로파일 설정
- **dev**: 개발 환경 (기본)
- **service**: 운영 환경
- **sadmin**: 시스템 관리자 환경

### 로깅 설정 상세 분석

#### Log4j2 설정 구조
프로젝트는 환경별로 다른 Log4j2 설정을 사용합니다:

```
src/main/resources/
├── log4j2.xml                    # 기본 개발 환경
├── resources-dev/log4j2.xml      # 개발 환경
├── resources-service/log4j2.xml  # 운영 환경
└── resources-sadmin/log4j2.xml   # 시스템 관리자 환경
```

#### 환경별 로그 설정 비교

**1. 개발 환경 (dev)**
```xml
<!-- 로그 파일 위치: /DATA/keep/logs/ -->
<Properties>
    <Property name="LOG_PATH">/DATA/keep/logs</Property>
</Properties>

<!-- 로그 레벨: 상세한 디버깅 -->
<Logger name="com.intermorph" level="DEBUG" additivity="false">
    <AppenderRef ref="console"/>
    <AppenderRef ref="file"/>
</Logger>
<Logger name="egovframework" level="DEBUG" additivity="false">
    <AppenderRef ref="console"/>
</Logger>
```

**2. 운영 환경 (service)**
```xml
<!-- 로그 파일 위치: /opt/tomcat/logs -->
<Properties>
    <Property name="LOG_PATH">/opt/tomcat/logs</Property>
</Properties>

<!-- 로그 레벨: 에러 중심 -->
<Logger name="com.intermorph" level="ERROR" additivity="false">
    <AppenderRef ref="console"/>
    <AppenderRef ref="file"/>
</Logger>
<Logger name="egovframework" level="ERROR" additivity="false">
    <AppenderRef ref="console"/>
</Logger>
```

**3. 시스템 관리자 환경 (sadmin)**
```xml
<!-- 로그 파일 위치: /home/<USER>/jeus8_5/logs -->
<RollingFile name="STUDY_FILE"
    fileName="/home/<USER>/jeus8_5/logs/log4j/mng/lcms/study.log"
    filePattern="/home/<USER>/jeus8_5/logs/log4j/mng/lcms/study.log.%d{yyyy-MM-dd}">

    <!-- 1시간마다 롤링 -->
    <TimeBasedTriggeringPolicy interval="3600" modulate="true"/>
</RollingFile>
```

#### Appender 설정 분석

**1. Console Appender**
```xml
<Console name="console" target="SYSTEM_OUT">
    <PatternLayout pattern="%d %5p [%c] %m%n"/>
</Console>
```
- **패턴**: `날짜 로그레벨 [클래스명] 메시지`
- **출력**: 표준 출력 (콘솔)

**2. RollingFile Appender**
```xml
<!-- 시스템 로그 -->
<RollingFile name="file"
    fileName="${LOG_PATH}/system.log"
    filePattern="${LOG_PATH}/system.log.%d{yyyy-MM-dd}">
    <PatternLayout pattern="%d %5p [%c] %m%n"/>
    <Policies>
        <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
    </Policies>
    <DefaultRolloverStrategy max="30"/>
</RollingFile>

<!-- 학습 관련 로그 -->
<RollingFile name="STUDY_FILE"
    fileName="${LOG_PATH}/lcms/study.log"
    filePattern="${LOG_PATH}/lcms/study.log.%d{yyyy-MM-dd}">
    <PatternLayout pattern="%d %5p [%c] %m%n"/>
    <Policies>
        <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
    </Policies>
    <DefaultRolloverStrategy max="30"/>
</RollingFile>

<!-- 에러 로그 (개발환경만) -->
<RollingFile name="errorfile"
    fileName="${LOG_PATH}/error.log"
    filePattern="${LOG_PATH}/error.log.%d{yyyy-MM-dd}">
    <PatternLayout pattern="%d %5p [%c] %m%n"/>
    <Policies>
        <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
    </Policies>
    <DefaultRolloverStrategy max="30"/>
</RollingFile>
```

#### 로그 레벨 및 패키지별 설정

**1. SQL 로깅 (log4jdbc)**
```xml
<!-- 기본 SQL 로그 -->
<Logger name="java.sql" level="INFO" additivity="false">
    <AppenderRef ref="console"/>
</Logger>

<!-- SQL 실행 시간 포함 로그 -->
<Logger name="jdbc.sqltiming" level="INFO" additivity="false">
    <AppenderRef ref="console"/>
</Logger>
```

**2. 프레임워크 로깅**
```xml
<!-- eGovFramework -->
<Logger name="egovframework" level="DEBUG|ERROR" additivity="false">
    <AppenderRef ref="console"/>
</Logger>

<!-- Spring Framework -->
<Logger name="org.springframework" level="ERROR" additivity="false">
    <AppenderRef ref="console"/>
    <AppenderRef ref="file"/>
</Logger>

<!-- Spring MVC View -->
<Logger name="org.springframework.web.servlet.view" level="DEBUG|ERROR" additivity="false">
    <AppenderRef ref="console"/>
    <AppenderRef ref="file"/>
</Logger>
```

**3. 애플리케이션 로깅**
```xml
<!-- 메인 애플리케이션 -->
<Logger name="com.intermorph" level="DEBUG|ERROR" additivity="false">
    <AppenderRef ref="console"/>
    <AppenderRef ref="file"/>
</Logger>

<!-- 커스텀 로거 -->
<Logger name="STUDY_LOGGER" level="TRACE" additivity="false">
    <AppenderRef ref="console"/>
    <AppenderRef ref="STUDY_FILE"/>
</Logger>

<Logger name="TRACE_LOGGER" level="TRACE" additivity="false">
    <AppenderRef ref="console"/>
</Logger>
```

#### 롤링 정책 분석

**1. 시간 기반 롤링**
```xml
<Policies>
    <!-- 매일 자정에 롤링 (개발/운영) -->
    <TimeBasedTriggeringPolicy interval="1" modulate="true"/>

    <!-- 1시간마다 롤링 (시스템 관리자) -->
    <TimeBasedTriggeringPolicy interval="3600" modulate="true"/>
</Policies>
```

**2. 파일 보관 정책**
```xml
<!-- 최대 30개 롤오버 파일 유지 -->
<DefaultRolloverStrategy max="30"/>
```

#### SQL 로깅 설정 (log4jdbc)

**1. 의존성 설정**
```xml
<!-- pom.xml -->
<dependency>
    <groupId>com.googlecode.log4jdbc</groupId>
    <artifactId>log4jdbc</artifactId>
    <version>1.2</version>
    <exclusions>
        <exclusion>
            <artifactId>slf4j-api</artifactId>
            <groupId>org.slf4j</groupId>
        </exclusion>
    </exclusions>
</dependency>
```

**2. 데이터베이스 드라이버 설정**
```properties
# globals.properties (모든 환경 공통)
Globals.mysql.DriverClassName=net.sf.log4jdbc.DriverSpy
Globals.mysql.Url=****************************************
```

#### 로그 패턴 분석

**1. 표준 패턴**
```
%d %5p [%c] %m%n
```
- `%d`: 날짜/시간
- `%5p`: 로그 레벨 (5자리 고정)
- `%c`: 로거 이름 (클래스명)
- `%m`: 메시지
- `%n`: 줄바꿈

**2. 상세 패턴 (시스템 로그)**
```
%d %p %c{1.} [%t] %m%n
```
- `%c{1.}`: 클래스명 축약
- `%t`: 스레드명

**3. 간단 패턴 (학습 로그)**
```
%-5p: %c - %m%n
```
- `%-5p`: 왼쪽 정렬 로그 레벨

#### 환경별 로그 레벨 정책

| 환경 | com.intermorph | egovframework | org.springframework | Root |
|------|----------------|---------------|-------------------|------|
| **dev** | DEBUG | DEBUG | ERROR | ERROR |
| **service** | ERROR | ERROR | ERROR | ERROR |
| **sadmin** | ERROR | ERROR | ERROR | ERROR |

#### 특수 로거 활용

**1. STUDY_LOGGER**
- **용도**: 학습 관련 상세 로그
- **레벨**: TRACE
- **출력**: 콘솔 + 전용 파일 (study.log)

**2. TRACE_LOGGER**
- **용도**: 디버깅용 상세 추적
- **레벨**: TRACE
- **출력**: 콘솔만

#### 로그 파일 구조

**개발 환경**
```
/DATA/keep/logs/
├── system.log                    # 시스템 로그
├── error.log                     # 에러 로그
└── lcms/
    └── study.log                 # 학습 관련 로그
```

**운영 환경**
```
/opt/tomcat/logs/
├── system.log                    # 시스템 로그
└── lcms/
    └── study.log                 # 학습 관련 로그
```

**시스템 관리자 환경**
```
/home/<USER>/jeus8_5/logs/log4j/mng/
└── lcms/
    └── study.log                 # 학습 관련 로그
```

이러한 Log4j2 설정을 통해 환경별로 적절한 로그 레벨과 출력 방식을 제공하여 개발 시에는 상세한 디버깅 정보를, 운영 시에는 필수적인 에러 정보만을 기록하도록 최적화되어 있습니다.

## 📝 개발 가이드

### 문서화
- **README.md**: 프로젝트 개요
- **Maven Site**: 자동 문서 생성
- **JavaDoc**: API 문서

### 코딩 표준
- **Lombok** 사용 권장
- **Spock** 테스트 작성
- **Git 브랜치 관리 정책** 준수

## 🚀 배포 및 운영

### 배포 방식
- **WAR 파일**: `egovframework-all-in-one.war`
- **Tomcat 배포**: Maven Tomcat Plugin 사용
- **Docker 지원**: 컨테이너 배포 가능

### 모니터링
- **JaCoCo**: 테스트 커버리지
- **Maven Site**: 프로젝트 리포트
- **로그 모니터링**: Log4j2 기반

---

## 📞 연락처 및 지원

이 시스템은 **한림평생교육원**의 종합적인 학습관리시스템(LMS)으로, 과정 관리부터 수강생 관리, 콘텐츠 관리, 결제까지 교육기관 운영에 필요한 모든 기능을 포함하고 있습니다. eGovFramework 기반으로 구축되어 정부 표준 프레임워크의 안정성과 보안성을 확보하고 있습니다.

## 📚 Swagger API 문서화 설정 가이드

### 🔧 1. Maven 의존성 추가

`pom.xml`에 다음 Swagger 의존성을 추가하세요:

```xml
<!-- Swagger 의존성 추가 (Spring Framework 5.3.6 호환) -->
<dependency>
    <groupId>io.springfox</groupId>
    <artifactId>springfox-swagger2</artifactId>
    <version>3.0.0</version>
</dependency>

<dependency>
    <groupId>io.springfox</groupId>
    <artifactId>springfox-swagger-ui</artifactId>
    <version>3.0.0</version>
</dependency>

<dependency>
    <groupId>io.springfox</groupId>
    <artifactId>springfox-boot-starter</artifactId>
    <version>3.0.0</version>
</dependency>
```

### 🏗️ 2. Swagger 설정 클래스 생성

`src/main/java/com/intermorph/cmmn/config/SwaggerConfig.java` 파일을 생성하세요:

```java
package com.intermorph.cmmn.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

@Configuration
@EnableSwagger2
@EnableWebMvc
public class SwaggerConfig implements WebMvcConfigurer {

    @Bean
    public Docket api() {
        return new Docket(DocumentationType.SWAGGER_2)
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.intermorph"))
                .paths(PathSelectors.regex("/.*"))
                .build()
                .apiInfo(apiInfo());
    }

    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("한림평생교육원 LMS API")
                .description("한림평생교육원 학습관리시스템 REST API 문서")
                .version("1.0.0")
                .contact(new Contact("한림평생교육원", "http://hallym.ac.kr", "<EMAIL>"))
                .license("Apache License Version 2.0")
                .licenseUrl("https://www.apache.org/licenses/LICENSE-2.0")
                .build();
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("swagger-ui.html")
                .addResourceLocations("classpath:/META-INF/resources/");

        registry.addResourceHandler("/webjars/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/");
    }
}
```

### 🔧 3. Spring 설정 파일 수정

#### 3-1. context-common.xml 수정

`src/main/resources/egovframework/spring/com/context-common.xml`에 Swagger 설정을 추가하세요:

```xml
<!-- 기존 설정 유지하고 아래 내용 추가 -->

<!-- Swagger Configuration -->
<context:component-scan base-package="com.intermorph.cmmn.config" />

<!-- Swagger 리소스 핸들러 -->
<mvc:resources mapping="swagger-ui.html" location="classpath:/META-INF/resources/" />
<mvc:resources mapping="/webjars/**" location="classpath:/META-INF/resources/webjars/" />
```

#### 3-2. egov-com-servlet.xml 수정

`src/main/webapp/WEB-INF/config/egovframework/springmvc/egov-com-servlet.xml`에 다음을 추가하세요:

```xml
<!-- Swagger UI 리소스 매핑 -->
<mvc:resources mapping="/swagger-ui/**" location="classpath:/META-INF/resources/webjars/springfox-swagger-ui/" />
<mvc:resources mapping="/v2/api-docs" location="classpath:/META-INF/resources/" />
```

### 🎯 4. 컨트롤러에 Swagger 어노테이션 추가

기존 컨트롤러에 Swagger 어노테이션을 추가하세요. 예시:

```java
package com.intermorph.fctls.fcltsMng.web;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

@Controller
@Api(tags = "시설물 관리", description = "시설물 등록, 수정, 삭제, 조회 API")
public class IMFcltsMngMngController extends BaseController {

    @ApiOperation(
        value = "시설물 목록 조회",
        notes = "등록된 시설물 목록을 조회합니다.",
        response = ModelAndView.class
    )
    @ApiResponses({
        @ApiResponse(code = 200, message = "성공"),
        @ApiResponse(code = 400, message = "잘못된 요청"),
        @ApiResponse(code = 500, message = "서버 오류")
    })
    @RequestMapping(value = "/mng/fcltsMng/selectList.do")
    public ModelAndView selectList(
            HttpServletRequest req,
            HttpServletResponse res,
            @ApiParam(value = "검색 조건", required = false) IMFcltsMngCondition condition
    ) throws Exception {
        // 기존 코드 유지
    }

    @ApiOperation(value = "시설물 등록", notes = "새로운 시설물을 등록합니다.")
    @RequestMapping(value = "/mng/fcltsMng/insert.do")
    public ModelAndView insert(
            HttpServletRequest req,
            HttpServletResponse res,
            @ApiParam(value = "시설물 정보", required = true) @ModelAttribute("iMFcltsMng") IMFcltsMngVO iMFcltsMng,
            BindingResult bindingResult,
            ModelMap model,
            IMCmmnDescVO cmmnDesc
    ) throws Exception {
        // 기존 코드 유지
    }
}
```

### 🔧 5. VO 클래스에 Swagger 모델 어노테이션 추가

```java
package com.intermorph.fctls.fcltsMng.service;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "시설물 관리 정보")
public class IMFcltsMngVO extends BaseVO implements Serializable {

    @ApiModelProperty(value = "시설물관리ID", example = "FCLTS001", required = true)
    private String fcltsMngId;

    @ApiModelProperty(value = "장소구분코드", example = "01", notes = "01=교육원, 02=서울교육장, 03=대전교육센터")
    private String placeDvsnCdv;

    @ApiModelProperty(value = "시설물구분코드", example = "01", notes = "01=강의장, 02=기숙사, 03=운동장, 04=회의실")
    private String fcltsDvsnCdv;

    @ApiModelProperty(value = "시설물명", example = "제1강의실", required = true)
    private String fcltsNm;

    @ApiModelProperty(value = "층수", example = "01", notes = "01=1층, 02=2층, 03=3층, 04=4층, 05=5층, 99=층수없음")
    private String floorCdv;

    @ApiModelProperty(value = "수용인원", example = "50")
    private String acmdPrsnl;

    @ApiModelProperty(value = "상태코드", example = "01", notes = "01=정상, 02=고장, 03=수리, 04=기타")
    private String sttsCdv;

    @ApiModelProperty(value = "사용여부", example = "Y", allowableValues = "Y,N")
    private String useYn;

    // getter/setter 메소드들...
}
```

### 🌐 6. 웹 설정 수정 (선택사항)

만약 `web.xml` 대신 Java Config를 사용한다면, `EgovWebApplicationInitializer.java`를 수정하세요:

```java
// EgovWebApplicationInitializer.java에 추가
@Override
public void onStartup(ServletContext servletContext) throws ServletException {
    // 기존 코드 유지...

    // Swagger UI 접근을 위한 설정
    ServletRegistration.Dynamic swaggerServlet = servletContext.addServlet("swagger", new DispatcherServlet());
    swaggerServlet.addMapping("/swagger-ui/*");
    swaggerServlet.setLoadOnStartup(2);
}
```

### 🚀 7. 접근 URL

설정 완료 후 다음 URL로 Swagger 문서에 접근할 수 있습니다:

- **Swagger UI**: `http://localhost:8080/swagger-ui.html`
- **API JSON**: `http://localhost:8080/v2/api-docs`

### 🔒 8. 보안 설정 (선택사항)

만약 Spring Security를 사용 중이라면, Swagger URL에 대한 접근 권한을 설정해야 합니다:

```java
// SecurityConfig.java 또는 해당 보안 설정 파일에 추가
.antMatchers("/swagger-ui.html", "/swagger-ui/**", "/v2/api-docs", "/webjars/**").permitAll()
```

### 📝 9. 추가 설정 옵션

#### 9-1. API 그룹별 분리

```java
@Bean
public Docket facilityApi() {
    return new Docket(DocumentationType.SWAGGER_2)
            .groupName("시설 관리")
            .select()
            .apis(RequestHandlerSelectors.basePackage("com.intermorph.fctls"))
            .paths(PathSelectors.regex("/mng/fclts.*"))
            .build()
            .apiInfo(facilityApiInfo());
}

@Bean
public Docket courseApi() {
    return new Docket(DocumentationType.SWAGGER_2)
            .groupName("과정 관리")
            .select()
            .apis(RequestHandlerSelectors.basePackage("com.intermorph.crs"))
            .paths(PathSelectors.regex("/mng/crs.*"))
            .build()
            .apiInfo(courseApiInfo());
}
```

#### 9-2. 환경별 Swagger 활성화

```java
@Bean
@Profile({"dev", "test"}) // 개발/테스트 환경에서만 활성화
public Docket api() {
    return new Docket(DocumentationType.SWAGGER_2)
            // 설정 내용...
}
```

### ⚠️ 주의사항

1. **eGovFramework 호환성**: eGovFramework와의 호환성을 위해 SpringFox 3.0.0 버전을 권장합니다.
2. **성능 고려**: 운영 환경에서는 Swagger를 비활성화하는 것을 권장합니다.
3. **보안**: API 문서에 민감한 정보가 노출되지 않도록 주의하세요.
4. **URL 매핑**: 기존 URL 매핑과 충돌하지 않도록 확인하세요.

이 설정을 통해 한림평생교육원 LMS의 모든 API를 체계적으로 문서화하고, 개발자들이 쉽게 API를 이해하고 테스트할 수 있는 환경을 제공할 수 있습니다.
